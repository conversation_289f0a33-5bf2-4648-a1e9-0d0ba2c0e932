
.recently-added {
    width: 100%;
    padding: 4px 12px;
    padding-right: 4px;
    border-radius: 36px;
    border: 1px solid var(--border-to);
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.recently-added a {
    display: flex;
    font-size: small;
    padding: 6px 12px;
    border-radius: 36px;
    background: var(--light);
    color: var(--dark);
    align-items: center;
}
.recently-added a ion-icon {
    margin-right: 4px;
}

.recently-added h4 {
    font-size: small;
    color: var(--dark);
}
.course-carousel {
    width: 100%;
    border-radius: 16px;
    background-image: var(--grey);
    height: auto;
    padding: 0;
    padding-top: 12px;
    min-height: 24vh;
}
.main-carousel {
    width: 100%;
    height: calc(100% - 24px); /* Set a fixed height to match the items */
    overflow-x: auto; /* Allows horizontal scrolling */
    display: flex;
    scroll-snap-type: x mandatory; /* Enables snapping to each item */
    scroll-behavior: smooth; /* Smooth scroll effect */
    border-radius: 16px;
}
.main-carousel::-webkit-scrollbar {
    border-radius: 12px;
}
.main-carousel::-webkit-scrollbar-track {
    border-radius: 12px;
}
.main-carousel::-webkit-scrollbar-thumb {
    border-radius: 12px;
}
.main-carousel p {
    color: var(--dark);
}

.carousel-item {
    width: 400px;
    background-color: var(--light); /* Optional: for better visibility */
    flex: 0 0 auto; /* Prevents resizing of items */
    scroll-snap-align: start; /* Snaps each item to the start */
    border-radius: 12px;
    margin-bottom: 12px;
    box-shadow: 2px 4px 12px #0001;
}
.carousel-item:not(:first-child) {
    margin-left: 12px;
}
.carousel-card {
    width: 100%;
    height: 100%;
    padding: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

}
.carousel-card span {
    font-size: 12px;
    color: var(--dark-grey);
    user-select: none !important;
}
.carousel-card h3 {
    font-size: medium;
    margin-top: 24px;
    margin-bottom: 24px;
    user-select: none !important;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}
.carousel-card p {
    margin-bottom: 24px;
    color: var(--dark-grey);
    user-select: none !important;
}
.carousel-card p:nth-child(3) {
    padding: 6px 12px;
    border-radius: 36px;
    background: linear-gradient(to right,var(--list), transparent);
    text-overflow: ellipsis;
    font-size: 11px !important;
    white-space: nowrap;
    overflow: hidden;
}
.carousel-card p:nth-child(4) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}
.carousel-card button {
    border: none;
    padding: 8px 12px;
    background: #000;
    border-radius: 36px;
    color: #fff;
    user-select: none !important;
    transition: all 0.2s ease;
    margin-top: 12px;
}
.carousel-card button:hover {
    background: var(--btn-blue);
    color: #f4f4f4;
}

/*----Start-Course-List----*/

.course-list-title {
    width: 100%;
    padding: 6px;
    padding-left: 12px;
    border-radius: 36px;
    border: 1px solid var(--border-to);
    margin-bottom: 12px;
    margin-top: 12px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.course-list-title h4 {
    font-size: small;
    color: var(--dark);
}
#filtered-courses p {
    color: var(--dark);
}

.filter-course select {
    padding: 6px 12px;
    outline: 1px solid var(--border-to);
    border-radius: 36px !important;
    background: var(--light);
    color: var(--dark);
    border: none;
}
#filtered-courses {
    padding: 12px;
    border-radius: 16px;
    background: var(--light-gradient);
    min-height: calc(60vh - 36px);
}
.course-item {
    border: 1px solid var(--grey);
    margin-bottom: 6px;
    padding: 6px 24px;
    padding-right: 6px;
    padding-left: 10px;
    border-radius: 36px;
    display: flex;
}

.course-item:nth-child(odd) {
    background: var(--list);
}

.course-item h5 {
    font-size: small;
    flex: 1;
    grid-column: span 3;
    display: flex;
    align-items: center;
    color: var(--dark);
    margin-left: 42px;
}
.course-item h5 span {
    font-size: 12px;
    margin-left: 24px;
    font-weight: 400;
    color: green;
    display: flex;
    align-items: center;
}
.course-item h5 span ion-icon {
    margin-right: 4px;
}
.course-item button {
    justify-self: end;
    border: 1px solid #ccc;
    background: none;
    padding: 0 12px;
    color: var(--dark);
    height: 26px;
    display: block;
    font-size: small;
    margin-left: 42px;
}
.course-item p {
    display: flex;
    align-items: center;
    color: var(--dark-grey);
    white-space: nowrap;
}
.course-item p:first-child {
    font-size: 11px !important;
}
/*----End-Course-List----*/


.module-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;;
}
.ongoing-module {
    margin: 0 !important;
    color: green !important;
}
.ongoing-module ion-icon {
    margin-right: 4px;
}
.manage-module-button[disabled] {
    background-color: green; /* Bootstrap success green */
    color: white;
    cursor: not-allowed;
    opacity: 0.9;
    border: none;
}
.manage-module-button[disabled]:hover {
    background: var(--btn-blue);
}


@media screen and (max-width: 768px) {
    .carousel-item {
        width: 100%;
    }
    .filter-course {
        width: 50%;
    }
    .course-item {
        display: block;
        border-radius: 12px;
        padding: 24px;
    }
    .course-item p {
        margin-bottom: 12px;
    }
    .course-item h5 {
        margin-bottom: 12px;
    }
    .course-item button {
        width: 100%;
        margin-top: 24px;
        padding: 12px;
        height: auto;
    }
    .carousel-card button {
        padding: 12px 12px;
    }

}